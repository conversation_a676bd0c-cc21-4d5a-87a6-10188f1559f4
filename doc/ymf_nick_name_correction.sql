-- ========================================
-- 矫正 nhsa_3505 表中 ymf_nick_name 字段的 MySQL 语句
-- 根据 dept_id 字段值为空的 ymf_nick_name 字段赋值
-- ========================================

-- 1. 查看需要矫正的数据量（执行前检查）
SELECT 
    dept_id,
    COUNT(*) as record_count,
    CASE 
        WHEN dept_id = 1 THEN '门诊西药房'
        WHEN dept_id = 2 THEN '中药房'
        WHEN dept_id = 3 THEN '住院西药房'
        WHEN dept_id = 4 THEN '急诊药房'
        WHEN dept_id = 5 THEN '北院门诊西药房'
        WHEN dept_id = 6 THEN '北院门诊中药房'
        WHEN dept_id = 7 THEN '北区药房'
        WHEN dept_id = 8 THEN '五星药房'
        WHEN dept_id = 10 THEN '输液药房'
        WHEN dept_id = 11 THEN '静配中心'
        WHEN dept_id = 15 THEN '门诊慢性病药房'
        WHEN dept_id = 17 THEN '互联网医院专用药房'
        WHEN dept_id = 18 THEN '发热门诊药房'
        WHEN dept_id = 19 THEN 'ICU麻精药品基数管理系统'
        WHEN dept_id = 23 THEN 'CTMRI医技药房'
        WHEN dept_id = 24 THEN '手术麻醉药房'
        WHEN dept_id = 25 THEN '产房麻精药品基数管理系统'
        WHEN dept_id = 26 THEN '核医学药房'
        WHEN dept_id = 27 THEN 'GCP中心药房'
        WHEN dept_id = 29 THEN '电子处方流转药房'
        WHEN dept_id = 30 THEN 'GCP急诊医学科药房'
        WHEN dept_id = 32 THEN '煎药中心'
        ELSE CONCAT('未知药房(', dept_id, ')')
    END AS target_ymf_nick_name
FROM nhsa_3505 
WHERE (ymf_nick_name IS NULL OR ymf_nick_name = '')
    AND dept_id IS NOT NULL
    AND delete_flag = '0'
GROUP BY dept_id
ORDER BY dept_id;

-- 2. 执行矫正操作（主要的UPDATE语句）
UPDATE nhsa_3505 
SET 
    ymf_nick_name = CASE 
        WHEN dept_id = 1 THEN '门诊西药房'
        WHEN dept_id = 2 THEN '中药房'
        WHEN dept_id = 3 THEN '住院西药房'
        WHEN dept_id = 4 THEN '急诊药房'
        WHEN dept_id = 5 THEN '北院门诊西药房'
        WHEN dept_id = 6 THEN '北院门诊中药房'
        WHEN dept_id = 7 THEN '北区药房'
        WHEN dept_id = 8 THEN '五星药房'
        WHEN dept_id = 10 THEN '输液药房'
        WHEN dept_id = 11 THEN '静配中心'
        WHEN dept_id = 15 THEN '门诊慢性病药房'
        WHEN dept_id = 17 THEN '互联网医院专用药房'
        WHEN dept_id = 18 THEN '发热门诊药房'
        WHEN dept_id = 19 THEN 'ICU麻精药品基数管理系统'
        WHEN dept_id = 23 THEN 'CTMRI医技药房'
        WHEN dept_id = 24 THEN '手术麻醉药房'
        WHEN dept_id = 25 THEN '产房麻精药品基数管理系统'
        WHEN dept_id = 26 THEN '核医学药房'
        WHEN dept_id = 27 THEN 'GCP中心药房'
        WHEN dept_id = 29 THEN '电子处方流转药房'
        WHEN dept_id = 30 THEN 'GCP急诊医学科药房'
        WHEN dept_id = 32 THEN '煎药中心'
        ELSE CONCAT('未知药房(', dept_id, ')')
    END,
    update_by = 'system_correction',
    update_time = NOW()
WHERE 
    (ymf_nick_name IS NULL OR ymf_nick_name = '')
    AND dept_id IS NOT NULL
    AND delete_flag = '0';

-- 3. 验证矫正结果
SELECT 
    '矫正后验证' as check_type,
    dept_id,
    ymf_nick_name,
    COUNT(*) as record_count
FROM nhsa_3505 
WHERE dept_id IS NOT NULL
    AND delete_flag = '0'
    AND dept_id IN (1,2,3,4,5,6,7,8,10,11,15,17,18,19,23,24,25,26,27,29,30,32)
GROUP BY dept_id, ymf_nick_name
ORDER BY dept_id;

-- 4. 检查是否还有未处理的空值记录
SELECT 
    '未处理的空值检查' as check_type,
    COUNT(*) as empty_ymf_nick_name_count
FROM nhsa_3505 
WHERE (ymf_nick_name IS NULL OR ymf_nick_name = '')
    AND dept_id IS NOT NULL
    AND delete_flag = '0';

-- 5. 按药房统计矫正后的数据分布
SELECT 
    ymf_nick_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN drug_trac_info IS NOT NULL AND drug_trac_info != '' THEN 1 END) as with_trace_code,
    ROUND(
        (COUNT(CASE WHEN drug_trac_info IS NOT NULL AND drug_trac_info != '' THEN 1 END) * 100.0 / COUNT(*)), 
        2
    ) AS trace_code_rate
FROM nhsa_3505 
WHERE ymf_nick_name IS NOT NULL 
    AND ymf_nick_name != ''
    AND delete_flag = '0'
    AND sel_retn_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY ymf_nick_name
ORDER BY ymf_nick_name;

-- ========================================
-- 执行说明：
-- 1. 先执行第1个查询，查看需要矫正的数据量
-- 2. 确认无误后执行第2个UPDATE语句进行矫正
-- 3. 执行第3-5个查询验证矫正结果
-- ========================================




-- 基于ymf_nick_name的药房统计（合并门诊住院）
SELECT 
    DATE_FORMAT(sel_retn_time, '%Y-%m-%d') AS 统计日期,
    ymf_nick_name AS 药房名称,
    COUNT(*) AS 应采数,
    COUNT(CASE WHEN drug_trac_info IS NOT NULL AND drug_trac_info != '' THEN 1 END) AS 实采数,
    ROUND(
        (COUNT(CASE WHEN drug_trac_info IS NOT NULL AND drug_trac_info != '' THEN 1 END)  / COUNT(*)), 
        4
    ) AS 采集率百分比
FROM nhsa_3505 
WHERE 
    sel_retn_time IS NOT NULL 
    -- AND sd_dps IN ('1', '2')  -- 包含门诊和住院
    AND delete_flag = '0'
    AND ymf_nick_name IS NOT NULL
    AND sel_retn_time >= DATE_SUB(CURDATE(), INTERVAL 60 DAY) -- 最近30天
GROUP BY 
    DATE_FORMAT(sel_retn_time, '%Y-%m-%d'),
    ymf_nick_name
ORDER BY 
    药房名称 ASC,
    统计日期 DESC;
