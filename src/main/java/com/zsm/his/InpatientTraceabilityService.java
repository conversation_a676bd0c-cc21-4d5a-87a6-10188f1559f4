package com.zsm.his;

import com.zsm.model.ApiResult;

/**
 * 住院病人追溯码服务
 *
 * <AUTHOR>
 * @date 2025/8/19 下午9:31
 */
public interface InpatientTraceabilityService {

    /**
     * 处理住院患者药品追溯码补录和上传流程
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link ApiResult }<{@link String }>
     */
    ApiResult<String> processWeeklyInpatientTraceability(String startDate, String endDate);
}
