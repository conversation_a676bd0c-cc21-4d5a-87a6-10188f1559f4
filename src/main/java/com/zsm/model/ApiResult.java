package com.zsm.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一接口响应结果类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统一接口响应结果")
public class ApiResult<T> {

    /**
     * 响应状态码
     */
    @Schema(description = "响应状态码", example = "200")
    private Integer code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "操作成功")
    private String msg;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private T data;

    /**
     * 成功响应（无数据）
     *
     * @return ApiResult
     */
    public static <T> ApiResult<T> success() {
        return new ApiResult<>(200, "操作成功", null);
    }

    /**
     * 成功响应（有数据）
     *
     * @param data 响应数据
     * @return ApiResult
     */
    public static <T> ApiResult<T> success(T data) {
        return new ApiResult<>(200, "操作成功", data);
    }

    /**
     * 成功响应（自定义消息）
     *
     * @param message 响应消息
     * @return ApiResult
     */
    public static <T> ApiResult<T> success(String message) {
        return new ApiResult<>(200, message, null);
    }

    /**
     * 成功响应（自定义消息和数据）
     *
     * @param message 响应消息
     * @param data    响应数据
     * @return ApiResult
     */
    public static <T> ApiResult<T> success(String message, T data) {
        return new ApiResult<>(200, message, data);
    }

    /**
     * 失败响应（默认状态码500）
     *
     * @param message 错误消息
     * @return ApiResult
     */
    public static <T> ApiResult<T> error(String message) {
        return new ApiResult<>(500, message, null);
    }

    /**
     * 失败响应（自定义状态码）
     *
     * @param code    状态码
     * @param message 错误消息
     * @return ApiResult
     */
    public static <T> ApiResult<T> error(Integer code, String message) {
        return new ApiResult<>(code, message, null);
    }

    /**
     * 失败响应（自定义状态码和数据）
     *
     * @param code    状态码
     * @param message 错误消息
     * @param data    响应数据
     * @return ApiResult
     */
    public static <T> ApiResult<T> error(Integer code, String message, T data) {
        return new ApiResult<>(code, message, data);
    }

    /**
     * 根据布尔值返回成功或失败
     *
     * @param success        是否成功
     * @param successMessage 成功消息
     * @param errorMessage   失败消息
     * @return ApiResult
     */
    public static <T> ApiResult<T> result(boolean success, String successMessage, String errorMessage) {
        return success ? success(successMessage) : error(errorMessage);
    }

    /**
     * 根据布尔值返回成功或失败（带数据）
     *
     * @param success        是否成功
     * @param successMessage 成功消息
     * @param errorMessage   失败消息
     * @param data           响应数据
     * @return ApiResult
     */
    public static <T> ApiResult<T> result(boolean success, String successMessage, String errorMessage, T data) {
        return success ? success(successMessage, data) : error(errorMessage);
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this.code != null && this.code == 200;
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
} 