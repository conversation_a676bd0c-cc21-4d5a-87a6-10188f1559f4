package com.zsm.controller;

import cn.hutool.core.date.DateUtil;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.model.ApiResult;
import com.zsm.model.TableDataInfo;
import com.zsm.model.domain.DateRange;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.nhsa.request.fsi5204.Selinfo5204;
import com.zsm.model.nhsa.request.fsi5303.Fsi5303;
import com.zsm.model.nhsa.request.fsi5303.Selinfo5303;
import com.zsm.model.nhsa.response.Fsi5204Response;
import com.zsm.model.nhsa.response.Fsi5303Response;
import com.zsm.model.nhsa.response.Fsi5303OutputWrapper;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.vo.ProcessingResultVo;
import com.zsm.service.DispensingService;
import com.zsm.service.HangChuangService;
import com.zsm.service.Nhsa3505Service;
import com.zsm.superset.TraceabilityService;
import com.zsm.utils.DateRangeUtil;
import com.zsm.utils.NhsaHttpUtil;
import com.zsm.utils.NhsaRetryUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 3505销售记录报文表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Slf4j
@Tag(name = "3505销售记录", description = "3505销售记录报文表管理接口")
@RestController
@RequestMapping("/nhsa3505")
public class Nhsa3505Controller {

    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private TraceabilityService traceabilityService;
    @Resource
    private HangChuangService hangChuangService;
    @Resource
    private DispensingService dispensingService;
    @Resource
    private NhsaRetryUtil nhsaRetryUtil;

    @Operation(summary = "手动上传数据到两定接口平台", description = "手动触发数据上传到两定接口平台，支持按条件筛选上传")
    @PostMapping("/manualUpload")
    public ApiResult<String> manualUploadDataToPlatform(
            @Parameter(description = "处方明细序号，可选参数，如果传入则只上传对应的数据")
            @RequestParam(value = "cfmxxh", required = false) String cfmxxh,
            @Parameter(description = "处方序号，可选参数，如果传入则只上传对应的数据")
            @RequestParam(value = "cfxh", required = false) String cfxh) {
        return nhsa3505Service.manualUploadDataToPlatform(cfmxxh, cfxh);
    }

    @Operation(summary = "手动上传本周期内有效数据到两定接口平台", description = "手动触发数据上传到两定接口平台")
    @PostMapping("/manualUploadWeek")
    public ApiResult<String> manualUploadWeek() {
        nhsa3505Service.uploadDataToPlatform();
        return ApiResult.success();
    }

    @Operation(summary = "上传SuperSet统计数据", description = "手动触发上传3505数据统计信息到SuperSet平台，支持指定日期范围，不传参数默认统计今天的数据")
    @GetMapping("/uploadSuperSetData")
    public ApiResult<String> uploadSuperSetData(
            @Parameter(description = "开始日期，格式：yyyy-MM-dd，不传默认为今天")
            @RequestParam(value = "startDate", required = false) String startDate,
            @Parameter(description = "结束日期，格式：yyyy-MM-dd，不传默认为今天")
            @RequestParam(value = "endDate", required = false) String endDate) {
        return traceabilityService.generateAndUploadToSuperSet(startDate, endDate);
    }

    @GetMapping("/get5204Demo")
    @Operation(summary = "5204示例查询", description = "可传入就诊ID(mdtrtId)与人员编号(psnNo)任意一个或两个，当二者均为空时使用默认示例值")
    public ApiResult<List<Fsi5204Response>> get5204Demo(
            @RequestParam(value = "mdtrtId", required = false) @Parameter(description = "医保就诊ID，可选") String mdtrtId,
            @RequestParam(value = "psnNo", required = false) @Parameter(description = "人员编号，可选") String psnNo) {

        // 从缓存获取签名号，如果缓存不存在会自动获取并缓存
        Selinfo5204 selinfo5204 = new Selinfo5204();

        String defaultMdtrtId = "34122025080592436397";
        String defaultPsnNo = "34120000000016483603";

        boolean mdtrtIdBlank = (mdtrtId == null || mdtrtId.trim().isEmpty());
        boolean psnNoBlank = (psnNo == null || psnNo.trim().isEmpty());

        if (mdtrtIdBlank && psnNoBlank) {
            selinfo5204.setMdtrt_id(defaultMdtrtId);
            selinfo5204.setPsn_no(defaultPsnNo);
        } else {
            if (!mdtrtIdBlank) {
                selinfo5204.setMdtrt_id(mdtrtId.trim());
            }
            if (!psnNoBlank) {
                selinfo5204.setPsn_no(psnNo.trim());
            }
        }

        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        
        // 调用5204接口（带自动重试）
        NhsaCityResponse<List<Fsi5204Response>> response = nhsaRetryUtil.executeWithRetry(nhsaAccount, 
            currentSignNo -> NhsaHttpUtil.fsi5204(currentSignNo, selinfo5204, nhsaAccount));
        final List<Fsi5204Response> collect = response.getBody().getOutput().stream()
                .filter(item -> "09".equals(item.getMedChrgitmType()))
                .collect(Collectors.toList());
        log.info("只展示药品类型数量 {}",collect.size());
        return ApiResult.success(collect);

    }


    /**
     * 处理指定时间段的住院拆零确认数据
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 处理结果详情，包含HIS接口获取数量和确认的拆药数量
     */
    @GetMapping("/processTimeSlot")
    @Operation(summary = "处理指定时间段的住院拆零确认数据", description = "手动触发指定时间段的住院拆零确认处理，返回详细的处理统计信息")
    public ApiResult<ProcessingResultVo> processTimeSlot(@RequestParam(value = "startTime", defaultValue = "2025-08-02 13:52:00") String startTime,
                                                         @RequestParam(value = "endTime", defaultValue = "2025-08-02 14:53:00") String endTime) {
        try {
            // 解析时间参数
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);

            // 直接调用服务处理并返回详细结果（包含异常处理）
            return dispensingService.processInpatientDispenseConfirmationByTimeRange(start, end);
        } catch (Exception e) {
            // 仅处理时间解析异常，返回参数错误信息
            ProcessingResultVo errorResult = ProcessingResultVo.builder()
                    .success(false)
                    .message("时间参数解析异常：" + e.getMessage())
                    .hisDataCount(0)
                    .splitDrugCount(0)
                    .confirmedCount(0)
                    .skippedCount(0)
                    .failedCount(0)
                    .accountDetails(new ArrayList<>())
                    .build();
            return ApiResult.error(400, "参数错误，请检查时间格式（yyyy-MM-dd HH:mm:ss）：" + e.getMessage(), errorResult);
        }
    }

    /**
     * 查询今日出院患者并补录追溯码
     * 实现出院患者药品追溯码补录和上传的完整业务流程
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate   结束日期，格式：yyyy-MM-dd
     * @return 处理结果
     */
    @Operation(summary = "查询今日住院患者并补录追溯码(手动触发)", description = "查询指定时间范围内的出院患者，补录缺失的药品追溯码并上传到医保平台")
    @GetMapping("/queryTodayDischargedPatients")
    public ApiResult<String> queryTodayDischargedPatients(
            @RequestParam(value = "startDate", required = false, defaultValue = "")
            @Parameter(description = "开始日期，格式：yyyy-MM-dd，默认为今天") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "")
            @Parameter(description = "结束日期，格式：yyyy-MM-dd，默认为今天") String endDate) {

        try {
            // 参数校验和默认值处理
            if (startDate == null || startDate.trim().isEmpty()) {
                startDate = DateUtil.today();
            }
            if (endDate == null || endDate.trim().isEmpty()) {
                endDate = DateUtil.today();
            }

            // 日期格式校验（简单校验）
            if (!startDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return ApiResult.error("开始日期格式错误，请使用 yyyy-MM-dd 格式");
            }
            if (!endDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                return ApiResult.error("结束日期格式错误，请使用 yyyy-MM-dd 格式");
            }

            // 调用服务方法处理出院患者追溯码补录
            return hangChuangService.processWeeklyInpatientTraceability(startDate.trim(), endDate.trim());

        } catch (Exception e) {
            return ApiResult.error("处理出院患者追溯码补录时发生异常：" + e.getMessage());
        }
    }

    /**
     * 查询可以补录追溯码的药品明细（分页查询）
     * 查询指定时间范围内有药品发药记录但缺少追溯码的药品明细列表
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate   结束日期，格式：yyyy-MM-dd
     * @param pageNum   页码，默认为1
     * @param pageSize  每页大小，默认为50
     * @return 可补录追溯码的药品明细分页列表
     */
    @Operation(summary = "本次统计周期内可以补录追溯码的药品明细（分页查询）", description = "查询指定时间范围内有药品发药记录但缺少追溯码的药品明细列表，用于确定哪些药品需要补录追溯码，支持分页查询")
    @GetMapping("/queryPatientsNeedingTraceCode")
    public TableDataInfo queryPatientsNeedingTraceCode(
            @RequestParam(value = "startDate", required = false, defaultValue = "")
            @Parameter(description = "开始日期，格式：yyyy-MM-dd，默认为上周日") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "")
            @Parameter(description = "结束日期，格式：yyyy-MM-dd，默认为本周六") String endDate,
            @RequestParam(value = "pageNum", required = false, defaultValue = "1")
            @Parameter(description = "页码，默认为1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "50")
            @Parameter(description = "每页大小，默认为50") Integer pageSize) {

        DateRange weeklyRange = DateRangeUtil.calculateCurrentWeekRange();
        // 参数校验和默认值处理
        if (startDate == null || startDate.trim().isEmpty()) {
            startDate = weeklyRange.getStartDate();
        }
        if (endDate == null || endDate.trim().isEmpty()) {
            endDate = weeklyRange.getEndDate();
        }

        // 日期格式校验
        if (!startDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
            TableDataInfo errorResult = new TableDataInfo();
            errorResult.setCode(400);
            errorResult.setMsg("开始日期格式错误，请使用 yyyy-MM-dd 格式");
            errorResult.setRows(null);
            errorResult.setTotal(0);
            return errorResult;
        }
        if (!endDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
            TableDataInfo errorResult = new TableDataInfo();
            errorResult.setCode(400);
            errorResult.setMsg("结束日期格式错误，请使用 yyyy-MM-dd 格式");
            errorResult.setRows(null);
            errorResult.setTotal(0);
            return errorResult;
        }

        return nhsa3505Service.queryPatientsNeedingTraceCode(startDate.trim(), endDate.trim(), pageNum, pageSize);

    }

    /**
     * 执行追溯码复用
     * 将已上传成功的拆零药品追溯码复用到相同药品编码的未赋码记录上
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate   结束日期（格式：yyyy-MM-dd）
     * @return 处理结果，包含详细的统计信息
     */
    @Operation(summary = "执行追溯码复用(作弊模式)", description = "将已上传成功的拆零药品追溯码复用到相同药品编码的未赋码记录上")
    @Parameters({
            @Parameter(name = "startDate", description = "开始日期（格式：yyyy-MM-dd）", required = true, example = "2025-08-03"),
            @Parameter(name = "endDate", description = "结束日期（格式：yyyy-MM-dd）", required = true, example = "2025-08-06")
    })
    @GetMapping("/reuseTraceCodes")
    public ApiResult<Map<String, Object>> reuseTraceCodes(
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {

        log.info("接收到追溯码复用请求，时间范围：{} 至 {}", startDate, endDate);

        // 参数验证
        if (startDate == null || startDate.trim().isEmpty()) {
            return ApiResult.error("开始日期不能为空");
        }
        if (endDate == null || endDate.trim().isEmpty()) {
            return ApiResult.error("结束日期不能为空");
        }

        // 日期格式验证
        if (!startDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
            return ApiResult.error("开始日期格式错误，请使用 yyyy-MM-dd 格式");
        }
        if (!endDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
            return ApiResult.error("结束日期格式错误，请使用 yyyy-MM-dd 格式");
        }

        try {
            // 日期逻辑验证
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            if (start.isAfter(end)) {
                return ApiResult.error("开始日期不能晚于结束日期");
            }

            // 限制查询范围不超过90天
            if (start.plusDays(8).isBefore(end)) {
                return ApiResult.error("查询时间范围不能超过90天");
            }

            // 调用服务层执行追溯码复用
            return nhsa3505Service.reuseTraceCodesForPeriod(startDate.trim(), endDate.trim());

        } catch (DateTimeParseException e) {
            log.error("日期解析错误", e);
            return ApiResult.error("日期格式解析错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("执行追溯码复用时发生异常", e);
            return ApiResult.error("执行失败：" + e.getMessage());
        }
    }

    @GetMapping("/get5303Demo")
    @Operation(summary = "5303示例查询", description = "查询医保5303接口数据，开始时间和结束时间为空时默认使用当天")
        public ApiResult<List<Fsi5303Response>> get5303Demo(
        @RequestParam(value = "begntime", required = false) String begntime,
            @RequestParam(value = "endtime", required = false) String endtime) {

        Selinfo5303 selinfo5303 = new Selinfo5303();
        
        // 处理开始时间：如果传入参数为空或空字符串，使用当天
        LocalDate beginTime;
        if (begntime == null || begntime.trim().isEmpty()) {
            beginTime = LocalDate.now();
        } else {
            try {
                beginTime = LocalDate.parse(begntime);
            } catch (Exception e) {
                log.error("开始时间格式错误：{}", begntime, e);
                return ApiResult.error("开始时间格式错误，请使用yyyy-MM-dd格式");
            }
        }
        
        // 处理结束时间：如果传入参数为空或空字符串，使用当天
        LocalDate endTime;
        if (endtime == null || endtime.trim().isEmpty()) {
            endTime = LocalDate.now();
        } else {
            try {
                endTime = LocalDate.parse(endtime);
            } catch (Exception e) {
                log.error("结束时间格式错误：{}", endtime, e);
                return ApiResult.error("结束时间格式错误，请使用yyyy-MM-dd格式");
            }
        }
        
        selinfo5303.setBegntime(beginTime);
        selinfo5303.setEndtime(endTime);

        try {
            
            // 获取医保账号信息
            NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
            
            // 构建请求对象
            Fsi5303 fsi5303 = Fsi5303.builder()
                    .data(selinfo5303)
                    .build();
            
            // 调用5303接口（带自动重试）
            NhsaCityResponse<Fsi5303OutputWrapper> response = nhsaRetryUtil.executeWithRetry(nhsaAccount,
                    currentSignNo -> NhsaHttpUtil.fsi5303(currentSignNo, fsi5303, nhsaAccount));
            
            if (response != null && response.getBody() != null && response.getBody().getOutput() != null 
                && response.getBody().getOutput().getData() != null) {
                List<Fsi5303Response> resultList = response.getBody().getOutput().getData();
                log.info("5303接口查询成功，返回数据条数：{}", resultList.size());
                return ApiResult.success(resultList);
            } else {
                log.warn("5303接口返回数据为空");
                return ApiResult.error("5303接口返回数据为空");
            }
            
        } catch (Exception e) {
            log.error("调用5303接口查询数据失败", e);
            return ApiResult.error("调用5303接口查询数据失败：" + e.getMessage());
        }
    }

    
}
