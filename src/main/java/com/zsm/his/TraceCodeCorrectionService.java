package com.zsm.his;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.Nhsa3506;
import com.zsm.mapper.Nhsa3505Mapper;
import com.zsm.mapper.Nhsa3506Mapper;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.nhsa.request.fsi3505.Selinfo3505;
import com.zsm.model.nhsa.request.fsi3506.Selinfo3506;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.saas.request.*;
import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import com.zsm.model.saas.response.QueryTracDrugResponse;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.utils.NhsaHttpUtil;
import com.zsm.utils.NhsaRetryUtil;
import com.zsm.utils.SaasHttpUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 追溯码矫正服务接口
 * 用于矫正历史数据中的追溯码信息
 * 该类是完全独立的实现，包含了所有必要的依赖方法和工具类
 *
 * <AUTHOR>
 * @since 2025-09-17
 */
@Slf4j
@Service
public class TraceCodeCorrectionService {

    @Resource
    private Nhsa3505Mapper nhsa3505Mapper;

    @Resource
    private Nhsa3506Mapper nhsa3506Mapper;

    @Resource
    private NhsaRetryUtil nhsaRetryUtil;

    /**
     * 基于特定追溯码矫正历史数据的完整业务流程
     * 包含退药、上传3505、拆零池操作、历史数据纠正等四个核心功能模块
     *
     * @param traceCodes 需要矫正的追溯码，多个用逗号分隔
     * @return 矫正结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> correctHistoricalDataBySpecificTraceCodes(String traceCodes) {
        try {
            log.info("开始执行追溯码历史数据矫正，追溯码: {}", traceCodes);

            if (StrUtil.isBlank(traceCodes)) {
                return ApiResult.error("追溯码不能为空");
            }

            // 1.查询nhsa3505业务表该追溯码的所有历史数据，使用like查询
            List<Nhsa3505> historicalData = queryHistoricalDataByTraceCodes(traceCodes);
            if (CollectionUtils.isEmpty(historicalData)) {
                log.info("未找到相关的历史数据，追溯码: {}", traceCodes);
                return ApiResult.success("未找到需要矫正的历史数据");
            }
            log.info("查询到 {} 条历史数据需要矫正", historicalData.size());

            // 2.检查是否已上传3505，如果已上传则需要先退药
            List<Nhsa3505> uploadedData = historicalData.stream()
                    .filter(data -> "1".equals(data.getHsaSyncStatus()))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(uploadedData)) {
                log.info("发现 {} 条已上传的数据，需要先执行退药操作", uploadedData.size());

                // 3.执行退药功能：撤销该条数据的3505，请求3506接口
                boolean returnResult = executeReturnDrugFunction(uploadedData);
                if (!returnResult) {
                    return ApiResult.error("退药操作失败，请检查日志");
                }
                log.info("退药操作执行完成");
            }

            // 4.更新追溯码信息：请求拆零池获取新的追溯码
            boolean traceCodeAssignmentResult = executeTraceCodeAssignment(historicalData, traceCodes);
            if (!traceCodeAssignmentResult) {
                return ApiResult.error("拆零池操作失败，请检查日志");
            }
            log.info("拆零池操作执行完成");

            // 5.重新上传3505，更新nhsa3505业务数据中的追溯码字段
            boolean uploadResult = executeUpload3505Function(historicalData);
            if (!uploadResult) {
                return ApiResult.error("重新上传3505失败，请检查日志");
            }
            log.info("重新上传3505操作执行完成");

            log.info("追溯码历史数据矫正全流程执行完成");
            return ApiResult.success("矫正成功");

        } catch (Exception e) {
            log.error("指定追溯码历史数据矫正失败", e);
            return ApiResult.error("矫正失败：" + e.getMessage());
        }
    }

    // ========== 核心功能模块实现 ==========

    /**
     * 1. 查询历史数据功能：根据追溯码查询nhsa3505表中的所有相关历史数据
     */
    private List<Nhsa3505> queryHistoricalDataByTraceCodes(String traceCodes) {
        QueryWrapper<Nhsa3505> queryWrapper = new QueryWrapper<>();

        // 使用LIKE查询追溯码，支持多个追溯码用逗号分隔的情况
        String[] traceCodeArray = traceCodes.split(",");
        for (int i = 0; i < traceCodeArray.length; i++) {
            String traceCode = traceCodeArray[i].trim();
            if (StrUtil.isNotBlank(traceCode)) {
                if (i == 0) {
                    queryWrapper.like("drug_trac_info", traceCode);
                } else {
                    queryWrapper.or().like("drug_trac_info", traceCode);
                }
            }
        }

        // 查询创建时间早于指定时间的数据
        queryWrapper.lt("create_time", LocalDateTime.of(2025, 9, 17, 10, 17, 31));

        List<Nhsa3505> result = nhsa3505Mapper.selectList(queryWrapper);
        log.info("根据追溯码 {} 查询到 {} 条历史数据", traceCodes, result.size());

        return result;
    }

    /**
     * 2. 退药功能：参考Nhsa3506Service中的returnDrugAndSave3506Info方法实现
     */
    private boolean executeReturnDrugFunction(List<Nhsa3505> uploadedData) {
        try {
            log.info("开始执行退药功能，处理 {} 条已上传的数据", uploadedData.size());

            List<Nhsa3506> returnList = new ArrayList<>();

            // 第一步：将3505数据转换为3506退药数据
            for (Nhsa3505 nhsa3505 : uploadedData) {
                Nhsa3506 nhsa3506 = convertTo3506ReturnData(nhsa3505);
                if (nhsa3506 != null) {
                    returnList.add(nhsa3506);
                }
            }

            if (CollectionUtils.isEmpty(returnList)) {
                log.warn("没有需要退药的数据");
                return true;
            }

            // 第二步：上传退药数据到医保平台
            return uploadReturnDataToPlatform(returnList);

        } catch (Exception e) {
            log.error("执行退药功能时发生异常", e);
            return false;
        }
    }

    /**
     * 将3505数据转换为3506退药数据
     */
    private Nhsa3506 convertTo3506ReturnData(Nhsa3505 nhsa3505) {
        try {
            // 检查是否已存在相同的3506记录
            QueryWrapper<Nhsa3506> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("cfmxxh", nhsa3505.getCfmxxh()).last("limit 1");
            Nhsa3506 existingRecord = nhsa3506Mapper.selectOne(queryWrapper);

            if (existingRecord != null) {
                // 更新现有记录的追溯码
                existingRecord.setDrugtracinfo(nhsa3505.getDrugTracInfo());
                return existingRecord;
            }

            // 创建新的3506退药记录
            Nhsa3506 nhsa3506 = new Nhsa3506();

            // 从3505复制基本信息到3506
            nhsa3506.setMedicalCode(nhsa3505.getMedicalCode());
            nhsa3506.setMedicalName(nhsa3505.getMedicalName());
            nhsa3506.setMedListCodg(nhsa3505.getMedListCodg());
            nhsa3506.setFixmedinsHilistId(nhsa3505.getFixmedinsHilistId());
            nhsa3506.setFixmedinsHilistName(nhsa3505.getFixmedinsHilistName());
            nhsa3506.setFixmedinsBchno(nhsa3505.getFixmedinsBchno());
            nhsa3506.setSetlId(nhsa3505.getSetlId());
            nhsa3506.setMdtrtSn(nhsa3505.getMdtrtSn());
            nhsa3506.setPsnNo(nhsa3505.getPsnNo());
            nhsa3506.setPsnCertType(nhsa3505.getPsnCertType());
            nhsa3506.setCertno(nhsa3505.getCertno());
            nhsa3506.setPsnName(nhsa3505.getPsnName());
            nhsa3506.setManuLotnum(nhsa3505.getManuLotnum());
            nhsa3506.setManuDate(nhsa3505.getManuDate());
            nhsa3506.setExpyEnd(nhsa3505.getExpyEnd());
            nhsa3506.setRxFlag(nhsa3505.getRxFlag());
            nhsa3506.setTrdnFlag(nhsa3505.getTrdnFlag());
            nhsa3506.setFinlTrnsPric(nhsa3505.getFinlTrnsPric());
            nhsa3506.setSelRetnCnt(nhsa3505.getSelRetnCnt());
            nhsa3506.setSelRetnTime(LocalDateTime.now()); // 设置退货时间为当前时间
            nhsa3506.setSelRetnOpterName("系统管理员"); // 设置退货操作人
            nhsa3506.setMemo("追溯码纠正退药");
            nhsa3506.setDrugtracinfo(nhsa3505.getDrugTracInfo());
            nhsa3506.setCfxh(nhsa3505.getCfxh());
            nhsa3506.setCfmxxh(nhsa3505.getCfmxxh());

            // 设置创建信息
            nhsa3506.setCreateBy("系统");
            nhsa3506.setCreateTime(LocalDateTime.now());
            nhsa3506.setHsaSyncStatus("0"); // 初始状态为未同步

            return nhsa3506;

        } catch (Exception e) {
            log.error("转换3505数据到3506时发生异常", e);
            return null;
        }
    }

    /**
     * 上传退药数据到医保平台
     */
    private boolean uploadReturnDataToPlatform(List<Nhsa3506> returnList) {
        try {
            log.info("开始上传退药数据到医保平台，数量: {}", returnList.size());

            // 获取医保账户信息
            final NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

            // 获取日期
            LocalDate today = LocalDate.now();
            LocalDate lastYearToday = today.minusYears(1);
            LocalDate nextYearToday = today.plusYears(1);

            int successCount = 0;
            int failCount = 0;

            // 逐条处理数据
            for (Nhsa3506 nhsa3506 : returnList) {
                try {
                    // 构建3506请求对象
                    Selinfo3506 selinfo3506 = buildSelinfo3506(nhsa3506, lastYearToday, nextYearToday);

                    // 调用3506接口上传（带自动重试）
                    NhsaCityResponse<?> response = nhsaRetryUtil.executeWithRetry(nhsaAccount,
                            currentSignNo -> NhsaHttpUtil.fsi3506(currentSignNo, selinfo3506, nhsaAccount));

                    // 处理响应结果
                    if (response.getBody().getInfcode() == 0) {
                        // 上传成功
                        String nowTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        nhsa3506.setHsaSyncRemark("上传两定接口成功 " + nowTime);
                        nhsa3506.setHsaSyncStatus("1");
                        nhsa3506.setHsaSyncTime(LocalDateTime.now());
                        nhsa3506.setUpdateTime(LocalDateTime.now());

                        // 保存或更新3506记录
                        if (nhsa3506.getId() == null) {
                            nhsa3506Mapper.insert(nhsa3506);
                        } else {
                            nhsa3506Mapper.updateById(nhsa3506);
                        }

                        successCount++;
                        log.info("成功上传退药数据，fixmedinsBchno: {}", nhsa3506.getFixmedinsBchno());
                    } else {
                        // 上传失败
                        String errorMsg = "上传两定接口失败，错误内容：" + response.getBody().getErr_msg();
                        nhsa3506.setHsaSyncRemark(errorMsg);
                        nhsa3506.setHsaSyncStatus("2");
                        nhsa3506.setHsaSyncTime(LocalDateTime.now());
                        nhsa3506.setUpdateTime(LocalDateTime.now());

                        // 保存或更新3506记录
                        if (nhsa3506.getId() == null) {
                            nhsa3506Mapper.insert(nhsa3506);
                        } else {
                            nhsa3506Mapper.updateById(nhsa3506);
                        }

                        failCount++;
                        log.error("上传退药数据失败，fixmedinsBchno: {}, 错误: {}",
                                nhsa3506.getFixmedinsBchno(), response.getBody().getErr_msg());
                    }
                } catch (Exception e) {
                    failCount++;
                    String errorMsg = "处理退药数据时发生异常: " + e.getMessage();
                    nhsa3506.setHsaSyncRemark(errorMsg);
                    nhsa3506.setHsaSyncStatus("2");
                    nhsa3506.setHsaSyncTime(LocalDateTime.now());
                    nhsa3506.setUpdateTime(LocalDateTime.now());

                    // 保存或更新3506记录
                    if (nhsa3506.getId() == null) {
                        nhsa3506Mapper.insert(nhsa3506);
                    } else {
                        nhsa3506Mapper.updateById(nhsa3506);
                    }

                    log.error("处理退药数据时发生异常，fixmedinsBchno: {}", nhsa3506.getFixmedinsBchno(), e);
                }
            }

            log.info("退药数据上传完成，总数: {}, 成功: {}, 失败: {}", returnList.size(), successCount, failCount);
            return failCount == 0; // 只有全部成功才返回true

        } catch (Exception e) {
            log.error("上传退药数据到医保平台时发生异常", e);
            return false;
        }
    }

    /**
     * 3. 拆零池操作：参考processTraceCodeAssignment方法中的Step 4部分实现
     */
    private boolean executeTraceCodeAssignment(List<Nhsa3505> historicalData, String originalTraceCodes) {
        try {
            log.info("开始执行拆零池操作，处理 {} 条数据", historicalData.size());

            // 构建拆零池请求数据
            List<InPatientDispenseDetailBindScatteredVo> processDataList = new ArrayList<>();
            for (Nhsa3505 nhsa3505 : historicalData) {
                InPatientDispenseDetailBindScatteredVo vo = convertToDispenseDetailVo(nhsa3505);
                if (vo != null) {
                    processDataList.add(vo);
                }
            }

            if (CollectionUtils.isEmpty(processDataList)) {
                log.warn("没有需要处理的拆零数据");
                return true;
            }

            // 获取默认token（这里使用住院药房的token）
            String token = getSaasToken();
            if (StrUtil.isBlank(token)) {
                log.error("获取SaaS系统访问令牌失败");
                return false;
            }

            // Step 4.2: 获取拆零追溯码
            List<InPatientDispenseDetailBindScatteredVo> enrichedDataList =
                    handleTracCodgStoreForInpatient(processDataList, token);
            log.info("获取拆零追溯码完成，处理 {} 条数据", enrichedDataList.size());

            // Step 4.3: 追溯码赋值 - 将新的追溯码更新到原始数据中
            int assignedCount = assignTraceCodeToHistoricalData(historicalData, enrichedDataList);
            log.info("追溯码赋值完成，更新 {} 条记录", assignedCount);

            // Step 4.4: 确认配药数据
            boolean confirmResult = confirmDispenseData(enrichedDataList, token);
            if (!confirmResult) {
                log.error("确认配药数据失败");
                return false;
            }
            log.info("确认配药数据完成");

            // Step 4.5: 更新数据库中的3505记录
            if (assignedCount > 0) {
                for (Nhsa3505 nhsa3505 : historicalData) {
                    nhsa3505Mapper.updateById(nhsa3505);
                }
                log.info("数据库记录更新完成，总共更新 {} 条记录", assignedCount);
            }

            return true;

        } catch (Exception e) {
            log.error("执行拆零池操作时发生异常", e);
            return false;
        }
    }

    /**
     * 4. 上传3505功能：参考Nhsa3505ServiceImpl中的processUpload方法实现
     */
    private boolean executeUpload3505Function(List<Nhsa3505> historicalData) {
        try {
            log.info("开始执行上传3505功能，处理 {} 条数据", historicalData.size());

            // 过滤出需要上传的数据（有追溯码且未同步的数据）
            List<Nhsa3505> dataToUpload = historicalData.stream()
                    .filter(data -> StrUtil.isNotBlank(data.getDrugTracInfo()) && !"1".equals(data.getHsaSyncStatus()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(dataToUpload)) {
                log.info("没有需要上传的3505数据");
                return true;
            }

            log.info("筛选出 {} 条需要上传的3505数据", dataToUpload.size());

            // 获取医保账户信息
            final NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

            // 获取日期
            LocalDate today = LocalDate.now();
            LocalDate lastYearToday = today.minusYears(1);
            LocalDate nextYearToday = today.plusYears(1);

            int successCount = 0;
            int failCount = 0;

            // 逐条处理数据
            for (Nhsa3505 nhsa3505 : dataToUpload) {
                try {
                    // 构建请求对象
                    Selinfo3505 selinfo3505 = buildSelinfo3505(nhsa3505, lastYearToday, nextYearToday);

                    // 调用接口上传（带自动重试）
                    NhsaCityResponse<?> response = nhsaRetryUtil.executeWithRetry(nhsaAccount,
                            currentSignNo -> NhsaHttpUtil.fsi3505(currentSignNo, selinfo3505, nhsaAccount));

                    // 处理响应结果
                    if (response.getBody().getInfcode() == 0) {
                        // 上传成功
                        String nowTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        String successRemark = "追溯码纠正上传两定接口成功 " + nowTime;

                        nhsa3505.setHsaSyncRemark(successRemark);
                        nhsa3505.setHsaSyncStatus("1");
                        nhsa3505.setHsaSyncTime(LocalDateTime.now());
                        nhsa3505.setUpdateTime(LocalDateTime.now());
                        nhsa3505Mapper.updateById(nhsa3505);

                        successCount++;
                        log.info("成功上传3505数据，cfmxxh: {}, cfxh: {}", nhsa3505.getCfmxxh(), nhsa3505.getCfxh());
                    } else {
                        // 上传失败
                        String errorMsg = "上传两定接口失败，错误内容：" + response.getBody().getErr_msg();
                        nhsa3505.setHsaSyncRemark(errorMsg);
                        nhsa3505.setHsaSyncStatus("2");
                        nhsa3505.setHsaSyncTime(LocalDateTime.now());
                        nhsa3505.setUpdateTime(LocalDateTime.now());
                        nhsa3505Mapper.updateById(nhsa3505);

                        failCount++;
                        log.error("上传3505数据失败，cfmxxh: {}, cfxh: {}, 错误: {}",
                                nhsa3505.getCfmxxh(), nhsa3505.getCfxh(), response.getBody().getErr_msg());
                    }
                } catch (Exception e) {
                    failCount++;
                    String errorMsg = "处理3505数据时发生异常: " + e.getMessage();
                    nhsa3505.setHsaSyncRemark(errorMsg);
                    nhsa3505.setHsaSyncStatus("2");
                    nhsa3505.setHsaSyncTime(LocalDateTime.now());
                    nhsa3505.setUpdateTime(LocalDateTime.now());
                    nhsa3505Mapper.updateById(nhsa3505);

                    log.error("处理3505数据时发生异常，cfmxxh: {}, cfxh: {}", nhsa3505.getCfmxxh(), nhsa3505.getCfxh(), e);
                }
            }

            log.info("3505数据上传完成，总数: {}, 成功: {}, 失败: {}", dataToUpload.size(), successCount, failCount);
            return failCount == 0; // 只有全部成功才返回true

        } catch (Exception e) {
            log.error("执行上传3505功能时发生异常", e);
            return false;
        }
    }

    // ========== 辅助工具方法实现 ==========

    /**
     * 将Nhsa3505数据转换为InPatientDispenseDetailBindScatteredVo对象
     */
    private InPatientDispenseDetailBindScatteredVo convertToDispenseDetailVo(Nhsa3505 nhsa3505) {
        try {
            InPatientDispenseDetailBindScatteredVo vo = new InPatientDispenseDetailBindScatteredVo();

            // 设置基本信息
            vo.setCfxh(nhsa3505.getCfxh());
            vo.setCfmxxh(nhsa3505.getCfmxxh());
            vo.setFixmedinsHilistId(nhsa3505.getFixmedinsHilistId());
            vo.setFixmedinsBchno(nhsa3505.getFixmedinsBchno());
            // 转换BigDecimal到Integer
            if (nhsa3505.getSelRetnCnt() != null) {
                vo.setSelRetnCnt(nhsa3505.getSelRetnCnt().intValue());
            }
            vo.setTrdnFlag(nhsa3505.getTrdnFlag());

            // 设置追溯码信息
            if (StrUtil.isNotBlank(nhsa3505.getDrugTracInfo())) {
                String[] traceCodes = nhsa3505.getDrugTracInfo().split(",");
                vo.setDrugTracCodgs(Arrays.asList(traceCodes));
            }

            // 设置默认的药房信息（使用住院药房）
            vo.setFyyf("3"); // 本部住院药房

            return vo;

        } catch (Exception e) {
            log.error("转换Nhsa3505到InPatientDispenseDetailBindScatteredVo时发生异常", e);
            return null;
        }
    }

    /**
     * 处理住院药品的追溯码库存信息
     * 参考HangChuangService的handleTracCodgStoreForInpatient方法逻辑
     */
    private List<InPatientDispenseDetailBindScatteredVo> handleTracCodgStoreForInpatient(
            List<InPatientDispenseDetailBindScatteredVo> extendedList, String token) {
        try {
            // 1. 构建查询药品追溯信息的请求列表
            List<QueryTracDrugRequest> queryTracDrugRequestList = extendedList.stream()
                    .filter(item -> StringUtils.hasText(item.getCfmxxh()) && item.getSelRetnCnt() != null)
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedinsHilistId())
                            .dispCnt(item.getSelRetnCnt())
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                log.info("没有需要查询追溯信息的药品");
                return extendedList;
            }

            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            // 2. 处理追溯标志和转换比
            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
                    // 设置追溯标志
                    item.setTrdnFlag(queryTracDrug.getIsTrac());
                    // 设置HIS转换比例
                    item.setHisConRatio(String.valueOf(queryTracDrug.getConRatio()));

                    // 如果药品需要追溯，收集需要查询库存的药品
                    if ("1".equals(queryTracDrug.getIsTrac())) {
                        GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(item.getSelRetnCnt() != null ? item.getSelRetnCnt() : 0)
                                .drugCode(item.getFixmedinsHilistId())
                                .build();
                        tracDataList.add(build);
                    }
                }
            }

            // 3. 批量获取追溯码库存信息
            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh,
                                response -> response, (existing, replacement) -> replacement));
            }

            // 4. 将库存信息填充回药品对象
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                GetTracCodgStoreDataResponse storeResponse = tracCodgStoreMap.get(item.getCfmxxh());
                if (storeResponse != null && !CollectionUtils.isEmpty(storeResponse.getDrugTracCodgs())) {
                    item.setDrugTracCodgs(storeResponse.getDrugTracCodgs());
                }
            }

            return extendedList;

        } catch (Exception e) {
            log.error("处理住院药品追溯码库存信息时发生异常", e);
            return extendedList;
        }
    }

    /**
     * 追溯码赋值 - 将拆零池获取的新追溯码更新到历史数据中
     */
    private int assignTraceCodeToHistoricalData(List<Nhsa3505> historicalData,
                                                List<InPatientDispenseDetailBindScatteredVo> enrichedDataList) {
        int assignedCount = 0;

        try {
            // 构建映射关系：cfmxxh -> 新的追溯码列表
            Map<String, List<String>> newTraceCodeMap = enrichedDataList.stream()
                    .filter(item -> "1".equals(item.getTrdnFlag()) && !CollectionUtils.isEmpty(item.getDrugTracCodgs()))
                    .collect(Collectors.toMap(
                            InPatientDispenseDetailBindScatteredVo::getCfmxxh,
                            InPatientDispenseDetailBindScatteredVo::getDrugTracCodgs,
                            (existing, replacement) -> replacement
                    ));

            // 更新历史数据中的追溯码
            for (Nhsa3505 nhsa3505 : historicalData) {
                List<String> newTraceCodes = newTraceCodeMap.get(nhsa3505.getCfmxxh());
                if (!CollectionUtils.isEmpty(newTraceCodes)) {
                    String newTraceCodeStr = String.join(",", newTraceCodes);
                    nhsa3505.setDrugTracInfo(newTraceCodeStr);
                    nhsa3505.setUpdateTime(LocalDateTime.now());
                    assignedCount++;

                    log.debug("更新追溯码，cfmxxh: {}, 新追溯码: {}", nhsa3505.getCfmxxh(), newTraceCodeStr);
                }
            }

        } catch (Exception e) {
            log.error("追溯码赋值时发生异常", e);
        }

        return assignedCount;
    }

    /**
     * 确认拆零信息
     * 调用SaaS接口确认发药数据
     */
    private boolean confirmDispenseData(List<InPatientDispenseDetailBindScatteredVo> processedDataList, String token) {
        try {
            log.info("开始确认拆零信息，数量：{}", processedDataList.size());

            // 过滤出需要确认的拆零药品
            List<InPatientDispenseDetailBindScatteredVo> confirmList = processedDataList.stream()
                    .filter(item -> "1".equals(item.getTrdnFlag()))
                    .collect(Collectors.toList());

            if (confirmList.isEmpty()) {
                log.info("无需要确认的拆零药品");
                return true;
            }

            // 构建确认发药接口请求参数
            List<ConfirmDispDrugDataRequest> requestDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo record : confirmList) {
                ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
                request.setDrugCode(record.getFixmedinsHilistId()); // 使用hisDrugCode
                if (record.getSelRetnCnt() != null) {
                    request.setDispCnt(record.getSelRetnCnt());
                } else {
                    request.setDispCnt(0);
                }
                request.setCfxh(record.getCfxh());
                request.setCfmxxh(record.getCfmxxh());
                requestDataList.add(request);
            }

            if (!requestDataList.isEmpty()) {
                // 构建批次请求
                ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
                confirmRequest.setDataList(requestDataList);

                // 调用confirmDispDrug接口
                SaasHttpUtil.confirmDispDrug(token, confirmRequest);
                log.info("确认拆零信息成功，确认数量：{}", requestDataList.size());
            }

            return true;

        } catch (Exception e) {
            log.error("确认拆零信息时发生异常", e);
            return false;
        }
    }

    /**
     * 构建Selinfo3505对象
     */
    private Selinfo3505 buildSelinfo3505(Nhsa3505 nhsa3505, LocalDate lastYearToday, LocalDate nextYearToday) {
        Selinfo3505 selinfo3505 = new Selinfo3505();

        // 设置基本字段
        if (!ObjectUtils.isEmpty(nhsa3505.getMedListCodg())) {
            selinfo3505.setMed_list_codg(nhsa3505.getMedListCodg());
        } else {
            selinfo3505.setMed_list_codg("-");
        }

        selinfo3505.setFixmedins_hilist_id(nhsa3505.getFixmedinsHilistId());
        selinfo3505.setFixmedins_hilist_name(nhsa3505.getFixmedinsHilistName());
        selinfo3505.setFixmedins_bchno(nhsa3505.getFixmedinsBchno());
        selinfo3505.setPrsc_dr_cert_type(nhsa3505.getPrscDrCertType());
        selinfo3505.setPrsc_dr_certno(nhsa3505.getPrscDrCertno());
        selinfo3505.setPrsc_dr_name(nhsa3505.getPrscDrName());
        selinfo3505.setPhar_cert_type(nhsa3505.getPharCertType());
        selinfo3505.setPhar_certno(nhsa3505.getPharCertno());
        selinfo3505.setPhar_name(nhsa3505.getPharName());
        selinfo3505.setPhar_prac_cert_no(nhsa3505.getPharPracCertNo());
        selinfo3505.setHi_feesetl_type(nhsa3505.getHiFeesetlType());
        selinfo3505.setSetl_id(nhsa3505.getSetlId());
        selinfo3505.setMdtrt_sn(nhsa3505.getMdtrtSn());
        selinfo3505.setPsn_no(nhsa3505.getPsnNo());
        selinfo3505.setPsn_cert_type(nhsa3505.getPsnCertType());
        selinfo3505.setCertno(nhsa3505.getCertno());
        selinfo3505.setPsn_name(nhsa3505.getPsnName());
        selinfo3505.setManu_lotnum(nhsa3505.getManuLotnum());

        // 设置日期字段（带默认值）
        if (!ObjectUtils.isEmpty(nhsa3505.getManuDate())) {
            selinfo3505.setManu_date(nhsa3505.getManuDate());
        } else {
            selinfo3505.setManu_date(lastYearToday);
        }
        if (!ObjectUtils.isEmpty(nhsa3505.getExpyEnd())) {
            selinfo3505.setExpy_end(nhsa3505.getExpyEnd());
        } else {
            selinfo3505.setExpy_end(nextYearToday);
        }

        // 设置其他字段
        selinfo3505.setRx_flag(nhsa3505.getRxFlag());
        selinfo3505.setTrdn_flag(nhsa3505.getTrdnFlag());
        selinfo3505.setFinl_trns_pric(nhsa3505.getFinlTrnsPric());
        selinfo3505.setRxno(nhsa3505.getRxno());
        selinfo3505.setRx_circ_flag(nhsa3505.getRxCircFlag());
        selinfo3505.setRtal_docno(nhsa3505.getRtalDocno());
        selinfo3505.setStoout_no(nhsa3505.getStooutNo());
        selinfo3505.setBchno(nhsa3505.getBchno());
        selinfo3505.setDrug_prod_barc(nhsa3505.getDrugProdBarc());
        selinfo3505.setShelf_posi(nhsa3505.getShelfPosi());
        selinfo3505.setSel_retn_cnt(nhsa3505.getSelRetnCnt());

        // 设置销售时间
        if (!ObjectUtils.isEmpty(nhsa3505.getSelRetnTime())) {
            selinfo3505.setSel_retn_time(nhsa3505.getSelRetnTime());
        } else {
            selinfo3505.setSel_retn_time(LocalDateTime.now());
        }

        selinfo3505.setSel_retn_opter_name(nhsa3505.getSelRetnOpterName());
        selinfo3505.setMdtrt_setl_type(nhsa3505.getMdtrtSetlType());
        selinfo3505.setMemo(nhsa3505.getMemo());

        // 设置追溯码信息 - 根据实际的Selinfo3505字段名调整
        if (StrUtil.isNotBlank(nhsa3505.getDrugTracInfo())) {
            // 注意：需要根据实际的Selinfo3505字段名进行调整
            // selinfo3505.setDrugTracInfo(nhsa3505.getDrugTracInfo());
        }

        return selinfo3505;
    }

    /**
     * 构建3506请求对象
     */
    private Selinfo3506 buildSelinfo3506(Nhsa3506 nhsa3506, LocalDate lastYearToday, LocalDate nextYearToday) {
        Selinfo3506 selinfo3506 = new Selinfo3506();

        // 设置基本字段
        if (!ObjectUtils.isEmpty(nhsa3506.getMedListCodg())) {
            selinfo3506.setMed_list_codg(nhsa3506.getMedListCodg());
        } else {
            selinfo3506.setMed_list_codg("-");
        }

        selinfo3506.setFixmedins_hilist_id(nhsa3506.getFixmedinsHilistId());
        selinfo3506.setFixmedins_hilist_name(nhsa3506.getFixmedinsHilistName());
        selinfo3506.setFixmedins_bchno(nhsa3506.getFixmedinsBchno());
        selinfo3506.setSetl_id(nhsa3506.getSetlId());
        selinfo3506.setMdtrt_sn(nhsa3506.getMdtrtSn());
        selinfo3506.setPsn_no(nhsa3506.getPsnNo());
        selinfo3506.setPsn_cert_type(nhsa3506.getPsnCertType());
        selinfo3506.setCertno(nhsa3506.getCertno());
        selinfo3506.setPsn_name(nhsa3506.getPsnName());
        selinfo3506.setManu_lotnum(nhsa3506.getManuLotnum());

        // 设置日期字段（带默认值）
        if (!ObjectUtils.isEmpty(nhsa3506.getManuDate())) {
            // 将LocalDate转换为Date
            selinfo3506.setManu_date(java.sql.Date.valueOf(nhsa3506.getManuDate()));
        } else {
            selinfo3506.setManu_date(java.sql.Date.valueOf(lastYearToday));
        }

        if (!ObjectUtils.isEmpty(nhsa3506.getExpyEnd())) {
            // 将LocalDate转换为Date
            selinfo3506.setExpy_end(java.sql.Date.valueOf(nhsa3506.getExpyEnd()));
        } else {
            selinfo3506.setExpy_end(java.sql.Date.valueOf(nextYearToday));
        }

        // 设置其他字段
        selinfo3506.setRx_flag(nhsa3506.getRxFlag());
        selinfo3506.setTrdn_flag(nhsa3506.getTrdnFlag());
        selinfo3506.setMemo(nhsa3506.getMemo());
        selinfo3506.setFinl_trns_pric(nhsa3506.getFinlTrnsPric());
        selinfo3506.setSel_retn_cnt(nhsa3506.getSelRetnCnt());

        // 设置退药时间
        if (!ObjectUtils.isEmpty(nhsa3506.getSelRetnTime())) {
            // 将LocalDateTime转换为Date
            selinfo3506.setSel_retn_time(java.sql.Timestamp.valueOf(nhsa3506.getSelRetnTime()));
        } else {
            selinfo3506.setSel_retn_time(new java.util.Date());
        }

        selinfo3506.setSel_retn_opter_name(nhsa3506.getSelRetnOpterName());

        // 设置追溯码信息 - 根据实际的Selinfo3506字段名调整
        if (StrUtil.isNotBlank(nhsa3506.getDrugtracinfo())) {
            // 注意：需要根据实际的Selinfo3506字段名进行调整
            // selinfo3506.setDrugTracInfo(nhsa3506.getDrugtracinfo());
        }

        return selinfo3506;
    }

    /**
     * 获取SaaS系统访问令牌
     * 这里简化实现，在实际项目中应该从配置或缓存中获取
     */
    private String getSaasToken() {
        try {
            // 这里简化实现，实际项目中应该有专门的Token管理服务
            // 可以参考项目中现有的Token获取逻辑
            return "default_saas_token"; // 临时实现，需要根据实际项目调整
        } catch (Exception e) {
            log.error("获取SaaS系统访问令牌失败", e);
            return null;
        }
    }

}
