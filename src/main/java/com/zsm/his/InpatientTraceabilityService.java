package com.zsm.his;

import com.zsm.model.ApiResult;
import com.zsm.model.vo.InpatientSettlementVo;
import com.zsm.model.domain.DateRange;

/**
 * 住院病人追溯码服务
 *
 * <AUTHOR>
 * @date 2025/8/19 下午9:31
 */
public interface InpatientTraceabilityService {

    /**
     * 处理住院患者药品追溯码补录和上传流程
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link ApiResult }<{@link String }>
     */
    ApiResult<String> processWeeklyInpatientTraceability(String startDate, String endDate);

    /**
	 * 处理单个住院患者的追溯码补录（使用周时间范围）
	 * 实现完整的业务流程：查询明细、比对差异、补录追溯码、上传数据
	 *
	 * @param patient 住院患者信息
	 * @param weeklyRange 周时间范围
	 * @return 处理是否成功
	 */
    boolean processInpatientWithWeeklyRange(InpatientSettlementVo patient, DateRange weeklyRange);
}
