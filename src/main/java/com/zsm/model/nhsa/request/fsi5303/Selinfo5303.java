package com.zsm.model.nhsa.request.fsi5303;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 5303接口入参报文
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Selinfo5303 {

    /**
     * 人员编号（字符型，长度30，非必填）
     */
    private String psn_no;

    /**
     * 开始时间（日期型，必填，格式：yyyy-MM-dd）
     */
    private LocalDate begntime;

    /**
     * 结束时间（日期型，必填，格式：yyyy-MM-dd）
     */
    private LocalDate endtime;

}
