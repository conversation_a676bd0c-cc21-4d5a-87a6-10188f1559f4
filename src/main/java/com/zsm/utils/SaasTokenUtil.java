package com.zsm.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zsm.constant.SyncAccountEnum;
import com.zsm.model.saas.response.AccessTokenReponse;
import com.zsm.service.TokenCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SaaS Token工具类
 * 负责基于发药药房ID(fyyf)获取访问令牌的相关逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class SaasTokenUtil {

    @Autowired
    private TokenCacheService tokenCacheService;

    /**
     * 根据发药药房ID(fyyf)获取对应账号的访问token（从缓存获取，失败则重新获取）
     * 
     * @param fyyf 发药药房ID
     * @return 访问令牌字符串，获取失败返回null
     */
    public String getTokenByFyyf(String fyyf) {
        try {
            log.info("开始根据发药药房ID获取token，fyyf: {}", fyyf);
            
            // 根据fyyf查找对应的账号配置
            SyncAccountEnum accountEnum = SyncAccountEnum.findByFyyf(fyyf);
            if (accountEnum == null) {
                log.error("未找到发药药房ID对应的账号配置，fyyf: {}", fyyf);
                return null;
            }

            // 先尝试从缓存中获取token
            AccessTokenReponse cachedToken = tokenCacheService.getTokenFromCache(accountEnum.getUsername());
            if (cachedToken != null && cachedToken.getReturnCode() == 0 && StrUtil.isNotBlank(cachedToken.getAuthorization())) {
                log.info("从缓存中获取到有效token，账号: {}", accountEnum.getUsername());
                return cachedToken.getAuthorization();
            }

            // 缓存中没有有效token，重新获取
            log.info("缓存中没有有效token，重新获取，账号: {}", accountEnum.getUsername());
            AccessTokenReponse newToken = SaasHttpUtil.getAccessToken(accountEnum.getUsername(), accountEnum.getPassword());
            
            if (newToken == null || newToken.getReturnCode() != 0 || StrUtil.isBlank(newToken.getAuthorization())) {
                log.error("获取token失败，账号: {}，响应为空或无效", accountEnum.getUsername());
                return null;
            }

            // 缓存新获取的token
            tokenCacheService.cacheToken(accountEnum.getUsername(), newToken);
            log.info("成功获取并缓存token，账号: {}", accountEnum.getUsername());
            
            return newToken.getAuthorization();
            
        } catch (Exception e) {
            log.error("根据发药药房ID获取token异常，fyyf: {}", fyyf, e);
            return null;
        }
    }

    /**
     * 根据发药药房ID(fyyf)获取完整的AccessTokenReponse数据（直接获取，不使用缓存）
     * 
     * @param fyyf 发药药房ID
     * @return AccessTokenReponse对象，获取失败返回null
     */
    public AccessTokenReponse getAccessTokenResponseByFyyf(String fyyf) {
        try {
            log.debug("开始根据发药药房ID获取AccessTokenReponse，fyyf: {}", fyyf);
            
            // 根据fyyf查找对应的账号配置
            SyncAccountEnum accountEnum = SyncAccountEnum.findByFyyf(fyyf);
            if (accountEnum == null) {
                log.error("未找到发药药房ID对应的账号配置，fyyf: {}", fyyf);
                return null;
            }

            // 直接从服务器获取token响应（不使用缓存）
            AccessTokenReponse tokenResponse = SaasHttpUtil.getAccessToken(accountEnum.getUsername(), accountEnum.getPassword());
            
            if (tokenResponse == null || tokenResponse.getReturnCode() != 0 || StrUtil.isBlank(tokenResponse.getAuthorization())) {
                log.error("获取AccessTokenReponse失败，账号: {}，响应为空或无效", accountEnum.getUsername());
                return null;
            }

            log.debug("成功获取AccessTokenReponse，账号: {}", JSONUtil.toJsonStr(tokenResponse));
            return tokenResponse;
            
        } catch (Exception e) {
            log.error("根据发药药房ID获取AccessTokenReponse异常，fyyf: {}", fyyf, e);
            return null;
        }
    }

    /**
     * 根据发药药房ID获取账号配置信息
     * 
     * @param fyyf 发药药房ID
     * @return 账号枚举配置，未找到返回null
     */
    public SyncAccountEnum getAccountByFyyf(String fyyf) {
        return SyncAccountEnum.findByFyyf(fyyf);
    }

    /**
     * 检查指定药房ID是否有对应的账号配置
     * 
     * @param fyyf 发药药房ID
     * @return true表示存在配置，false表示不存在
     */
    public boolean hasAccountConfig(String fyyf) {
        return SyncAccountEnum.findByFyyf(fyyf) != null;
    }
}
