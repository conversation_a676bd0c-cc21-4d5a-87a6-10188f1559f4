package com.zsm.his.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.config.HangChuangConfig;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.constant.SyncAccountEnum;
import com.zsm.entity.Nhsa3505;
import com.zsm.his.InpatientTraceabilityService;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.DateRange;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.nhsa.request.fsi5204.Selinfo5204;
import com.zsm.model.nhsa.response.Fsi5204Response;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.saas.request.*;
import com.zsm.model.saas.response.AccessTokenReponse;
import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import com.zsm.model.saas.response.QueryTracDrugResponse;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.model.vo.InpatientSettlementVo;
import com.zsm.service.Nhsa3505Service;
import com.zsm.service.TokenCacheService;
import com.zsm.utils.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 住院发药追溯码实现层
 * <p>
 * 需求：获取当日（或传入范围内）有发药数据的医保就诊患者，
 * 提取 mdtrt_sn 与 psn_no（若可用）调用 5204，完成追溯码赋码与上传。
 * <p>
 * 按照 HangChuangService.processWeeklyInpatientTraceability 的业务流程实现。
 *
 * <AUTHOR>
 * @date 2025/8/19 下午9:49
 */
@Slf4j
@Service
public class InpatientTraceabilityServiceImpl implements InpatientTraceabilityService {

    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private NhsaRetryUtil nhsaRetryUtil;
    @Resource
    private SaasTokenUtil saasTokenUtil;
    @Resource
    private HangChuangConfig hangChuangConfig;
    @Resource
    private TokenCacheService tokenCacheService;

    @Override
    public ApiResult<String> processWeeklyInpatientTraceability(String startDate, String endDate) {
        // 计算上周日到本周六的日期范围
        DateRange weeklyRange = DateRangeUtil.calculateCurrentWeekRange();
        log.info("开始处理住院患者药品追溯码补录，当前统计周期的,请检查`上周日`到`本周六`的时间范围：{} 至 {}",
                weeklyRange.getStartDate(), weeklyRange.getEndDate());

        try {
            // Step 1: 查询住院患者列表（使用周范围）
            List<InpatientSettlementVo> inpatients = queryInpatientsList(startDate, endDate);

            if (inpatients.isEmpty()) {
                log.info("指定时间范围内无住院患者");
                return ApiResult.success("指定时间范围内无住院患者");
            }

            // Step 2: 批量处理住院患者的追溯码补录
            return batchProcessInpatientTraceability(inpatients, weeklyRange);

        } catch (Exception e) {
            log.error("处理住院患者药品追溯码补录异常", e);
            return ApiResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 查询住院患者列表（重命名方法以适应新业务逻辑）
     * 调用HIS接口获取指定时间范围内的住院患者信息
     *
     * @param startDate 开始日期 yyyy-MM-dd
     * @param endDate   结束日期 yyyy-MM-dd
     * @return 住院患者列表
     */
    private List<InpatientSettlementVo> queryInpatientsList(String startDate, String endDate) {
        try {
            log.info("开始查询住院患者列表，时间范围：{} - {}", startDate, endDate);

            // 获取住院患者列表接口URL
            String requestUrl = hangChuangConfig.getInpatientSettlementUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executeGetRequest(requestUrl + "?start_time=" + startDate + "&end_time=" + endDate, "查询住院患者列表");

            // 解析响应结果
            List<InpatientSettlementVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InpatientSettlementVo.class);

            log.info("查询住院患者列表成功，返回{}条数据", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询住院患者列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量处理住院患者的追溯码补录
     */
    private ApiResult<String> batchProcessInpatientTraceability(List<InpatientSettlementVo> inpatients, DateRange weeklyRange) {
        int successPatients = 0;
        int failedPatients = 0;
        StringBuilder errorMessages = new StringBuilder();
        int totalPatients = inpatients.size();
        log.info("开始批量处理住院患者的追溯码补录，共 {} 位患者", totalPatients);

        for (InpatientSettlementVo patient : inpatients) {
            try {

                log.info("开始处理住院患者：{} (住院号：{})", patient.getPatientName(), patient.getPatInHosId());

                // 处理单个患者
                boolean result = processInpatientWithWeeklyRange(patient, null);

                if (result) {
                    successPatients++;
                    log.info("患者 {} 处理成功", patient.getPatientName());
                } else {
                    failedPatients++;
                    log.warn("患者 {} 处理失败", patient.getPatientName());
                    errorMessages.append("患者").append(patient.getPatientName()).append("处理失败; ");
                }

            } catch (Exception e) {
                failedPatients++;
                log.error("处理患者 {} 时发生异常", patient.getPatientName(), e);
                errorMessages.append("患者").append(patient.getPatientName()).append("异常: ").append(e.getMessage())
                        .append("; ");
            }

            // 每处理完一个患者，打印进度信息
            int processedCount = successPatients + failedPatients;
            int remainingCount = totalPatients - processedCount;
            log.info("进度更新: 已处理 {}/{} 位患者，剩余 {} 位患者 (成功: {}，失败: {})",
                    processedCount, totalPatients, remainingCount, successPatients, failedPatients);
        }

        String resultMessage = String.format("处理完成，成功: %d, 失败: %d", successPatients, failedPatients);
        if (!errorMessages.isEmpty()) {
            resultMessage += "，错误详情: " + errorMessages;
        }

        return ApiResult.success(resultMessage);
    }

    /**
     * 处理单个住院患者的追溯码补录（使用周时间范围）
     * 实现完整的业务流程：查询明细、比对差异、补录追溯码、上传数据
     *
     * @param patient     住院患者信息
     * @param weeklyRange 周时间范围
     * @return 处理是否成功
     */
    public boolean processInpatientWithWeeklyRange(InpatientSettlementVo patient, DateRange weeklyRange) {
        String patientName = patient.getPatientName();
        try {
            // 查询患者的5204费用明细（使用周时间范围）
            List<Fsi5204Response> feeDetails = queryFeeDetails(patient);
            log.info("患者 {} 查询到5204费用明细 {} 条,", patientName, feeDetails.size());

            // 如果提供了时间范围，则进行过滤；否则使用所有数据
            if (weeklyRange != null) {
                feeDetails = feeDetails.stream()
                        .filter(item -> isWithinDateRange(item.getFeeOcurTime(), weeklyRange.getStartDate(), weeklyRange.getEndDate()))
                        .collect(Collectors.toList());
                log.info("患者 {} 在指定时间范围内过滤后的5204费用明细 {} 条", patientName, feeDetails.size());
            } else {
                log.info("患者 {} 未指定时间范围，使用所有5204费用明细 {} 条", patientName, feeDetails.size());
            }

            if (feeDetails.isEmpty()) {
                log.info("患者 {} 无5204费用明细,跳过处理", patientName);
                return true; // 无数据不算失败
            }

            // Step 1: 查询该患者的住院发药明细
            List<InPatientDispenseDetailBindScatteredVo> patientDispenseDetails = queryPatientDispenseDetails(patient);
            log.info("患者 {} 查询到住院发药明细 {} 条", patientName, patientDispenseDetails.size());

            if (patientDispenseDetails.isEmpty()) {
                log.info("患者 {} 无住院发药明细,跳过处理", patientName);
                return true; // 无数据不算失败
            }

            patientDispenseDetails = filterInpatientDispensingData(feeDetails, patientDispenseDetails);

            // Step 1.5: 将住院发药明细增量保存到3505表
            log.info("患者 {} 开始将住院发药明细增量保存到3505表", patientName);
            try {
                // 使用同步方法保存住院发药数据到3505表
                nhsa3505Service.saveInpatientDataToNhsa3505(patientDispenseDetails);
                log.info("患者 {} 使用同步方法保存住院发药数据到3505表，数据量：{} 条",
                        patientName, patientDispenseDetails.size());
            } catch (Exception e) {
                log.error("患者 {} 保存住院发药明细到3505表时发生异常", patientName, e);
                // 不中断流程，继续后续处理
            }

            // Step 2: 查询已保存的3505业务数据
            List<Nhsa3505> existing3505Data = queryExisting3505Data(patient);
            log.info("患者 {} 已存在3505数据 {} 条", patientName, existing3505Data.size());

            // Step 3: 识别需要赋码上传的未同步数据
            List<Nhsa3505> unSyncedData = identifyUnSyncedDataWithFeeDetails(feeDetails, existing3505Data);

            if (unSyncedData.isEmpty()) {
                log.info("患者 {} 无需要处理的未同步数据", patientName);
                return true;
            }

            // Step 4: 处理追溯码赋值和拆零确认
            boolean traceCodeResult = processTraceCodeAssignment(unSyncedData, patientDispenseDetails, patientName);
            if (!traceCodeResult) {
                log.error("患者 {} 处理追溯码赋值失败", patientName);
                return false;
            }

            // Step 5: 处理未同步数据的赋码上传
            return processUnSyncedDataUpload(unSyncedData, patient);

        } catch (Exception e) {
            log.error("处理患者 {} 时发生异常", patientName, e);
            return false;
        }
    }

    /**
     * 判断费用发生时间是否在指定范围内
     */
    private boolean isWithinDateRange(LocalDateTime feeTime, String startDate, String endDate) {
        if (feeTime == null) return false;

        LocalDate feeDate = feeTime.toLocalDate();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        return !feeDate.isBefore(start) && !feeDate.isAfter(end);
    }

    /**
     * 查询5204费用明细（兼容方法）
     * 为了保持向后兼容性，保留原方法名
     *
     * @param patient 患者
     * @return {@link List }<{@link Fsi5204Response }>
     * @deprecated 请使用 queryFeeDetailsByDateRange 方法
     */
    @Deprecated
    private List<Fsi5204Response> queryFeeDetails(InpatientSettlementVo patient) {
        try {
            log.info("开始查询患者 {} 的5204费用明细", patient.getPatientName());

            Selinfo5204 selinfo5204 = new Selinfo5204();
            selinfo5204.setPsn_no(patient.getPsnNo());
            selinfo5204.setMdtrt_id(patient.getMdtrtSn());

            NhsaCityResponse<List<Fsi5204Response>> response = nhsaRetryUtil.executeWithRetry(NhsaAccountConstant.getNhsaAccount(),
                    currentSignNo -> NhsaHttpUtil.fsi5204(currentSignNo, selinfo5204, NhsaAccountConstant.getNhsaAccount()));

            return response.getBody().getOutput().stream().filter(item -> "09".equals(item.getMedChrgitmType()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询患者 {} 的5204费用明细异常", patient.getPatientName(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询患者的住院发药明细
     * 根据患者的住院ID查询所有相关的发药记录
     *
     * @param patient 出院患者信息
     * @return 住院发药明细列表
     */
    private List<InPatientDispenseDetailBindScatteredVo> queryPatientDispenseDetails(InpatientSettlementVo patient) {
        try {
            log.info("开始查询患者 {} 的住院发药明细", patient.getPatientName());

            // 构建查询参数 - 使用住院ID作为记录ID查询
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("pat_in_hos_id", patient.getPatInHosId());

            // 获取出院患者列表接口URL
            String requestUrl = hangChuangConfig.getInpatientDispenseDetailUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "查询住院发药明细");

            // 解析响应结果
            List<InPatientDispenseDetailBindScatteredVo> resultList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseDetailBindScatteredVo.class);
            resultList.forEach(item -> {
                item.setFixmedinsBchno(item.getIdFee());
            });

            log.info("查询住院发药明细成功，返回{}条数据", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询患者 {} 发药明细his接口异常", patient.getPatientName(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 过滤住院发药数据
     * 通过5204.rx_drord_no与住院发药数据进行关联比对
     *
     * @param feeDetails       5204费用明细列表
     * @param existing3505Data 已存在的3505数据
     * @return 需要处理的未同步数据列表
     */
    private List<InPatientDispenseDetailBindScatteredVo> filterInpatientDispensingData(List<Fsi5204Response> feeDetails,
                                                                                       List<InPatientDispenseDetailBindScatteredVo> existing3505Data) {
        log.info("开始识别需要赋码上传的未同步数据，5204明细{}条，住院处方明细数据{}条",
                feeDetails.size(), existing3505Data.size());

        // 构建5204费用明细的处方号集合
        Set<String> feeDetailRxNos = feeDetails.stream()
                .map(Fsi5204Response::getRxDrordNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<InPatientDispenseDetailBindScatteredVo> unSyncedData = existing3505Data.stream()
                .filter(data -> feeDetailRxNos.contains(data.getIdFee())) // 在5204中有对应记录
                .collect(Collectors.toList());

        log.info("识别到需要处理的3505未同步数据{}条", unSyncedData.size());
        return unSyncedData;
    }

    /**
     * 查询已存在的3505业务数据
     * 根据患者信息查询已保存在nhsa_3505表中的数据
     *
     * @param patient 出院患者信息
     * @return 已存在的3505数据列表
     */
    private List<Nhsa3505> queryExisting3505Data(InpatientSettlementVo patient) {
        String patientName = patient.getPatientName();
        try {
            log.info("开始查询患者 {} 的已存在3505数据", patientName);

            // 构建查询条件
            LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Nhsa3505::getMedicalCode, NhsaAccountConstant.getNhsaAccount().getMedicalCode());

            // 根据就医流水号查询（如果有的话）
            if (StrUtil.isNotBlank(patient.getMdtrtSn())) {
                queryWrapper.eq(Nhsa3505::getMdtrtSn, patient.getMdtrtSn());
            } else {
                // 如果没有就医流水号，根据患者姓名查询
                queryWrapper.eq(Nhsa3505::getPatientId, patient.getPatInHosId());
            }

            // 只查询住院数据
            queryWrapper.eq(Nhsa3505::getSdDps, SdDpsEnum.INPATIENT); // 1-住院

            List<Nhsa3505> existingData = nhsa3505Service.list(queryWrapper);
            log.info("患者 {} 查询到已存在3505数据 {} 条", patientName, existingData.size());

            return existingData;

        } catch (Exception e) {
            log.error("查询患者 {} 已存在3505数据异常", patientName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 识别需要赋码上传的未同步数据
     * 通过5204.rx_drord_no与3505.feedetl_sn进行关联比对
     *
     * @param feeDetails       5204费用明细列表
     * @param existing3505Data 已存在的3505数据
     * @return 需要处理的未同步数据列表
     */
    private List<Nhsa3505> identifyUnSyncedDataWithFeeDetails(List<Fsi5204Response> feeDetails, List<Nhsa3505> existing3505Data) {
        log.info("开始识别需要赋码上传的未同步数据，5204明细{}条，3505数据{}条",
                feeDetails.size(), existing3505Data.size());

        // 构建5204费用明细的处方号集合
        Set<String> feeDetailRxNos = feeDetails.stream()
                .map(Fsi5204Response::getRxDrordNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 筛选出符合条件的3505数据：
        // 1. hsa_sync_status = '0' (未同步)
        // 2. feedetl_sn 在5204的rx_drord_no中存在
        List<Nhsa3505> unSyncedData = existing3505Data.stream()
                .filter(data -> "0".equals(data.getHsaSyncStatus())) // 未同步
                .filter(data -> feeDetailRxNos.contains(data.getFeedetlSn())) // 在5204中有对应记录
                .collect(Collectors.toList());

        log.info("识别到需要处理的3505未同步数据{}条", unSyncedData.size());
        return unSyncedData;
    }

    /**
     * 处理追溯码赋值和拆零确认
     * 根据药品明细中的fyyf字段动态获取对应账号的token
     *
     * @param unSyncedData           未同步的3505数据列表
     * @param patientDispenseDetails 患者配药详情
     * @param patientName            患者姓名
     * @return 处理是否成功
     */
    private boolean processTraceCodeAssignment(List<Nhsa3505> unSyncedData, List<InPatientDispenseDetailBindScatteredVo> patientDispenseDetails, String patientName) {
        try {
            log.info("患者 {} 开始处理追溯码赋值和拆零确认，数据量：{}", patientName, unSyncedData.size());

            // Step 1: 数据过滤 - 筛选出需要处理的3505数据
            log.info("患者 {} Step 1: 数据过滤完成，筛选出 {} 条3505数据", patientName, unSyncedData.size());

            // 构建unSyncedData中fixmedinsBchno的集合用于匹配
            Set<String> unSyncedBchnoSet = unSyncedData.stream()
                    .filter(data -> StrUtil.isBlank(data.getDrugTracInfo()))
                    .map(Nhsa3505::getFixmedinsBchno)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 从完整的配药详情中筛选出与unSyncedData匹配的记录
            List<InPatientDispenseDetailBindScatteredVo> filteredDispenseDetails = patientDispenseDetails.stream()
                    .filter(detail -> unSyncedBchnoSet.contains(detail.getFixmedinsBchno()))
                    .collect(Collectors.toList());

            log.info("患者 {} Step 2: 提取患者配药详情完成，全量数据 {} 条，匹配筛选后 {} 条",
                    patientName, patientDispenseDetails.size(), filteredDispenseDetails.size());

            if (filteredDispenseDetails.isEmpty()) {
                log.error("患者 {} 无匹配的配药详情数据", patientName);
                return true;
            }

            // Step 3: 根据fyyf分组处理
            Map<String, List<InPatientDispenseDetailBindScatteredVo>> fyyfGroupMap = filteredDispenseDetails.stream()
                    .filter(detail -> StrUtil.isNotBlank(detail.getFyyf()))
                    .collect(Collectors.groupingBy(InPatientDispenseDetailBindScatteredVo::getFyyf));

            if (fyyfGroupMap.isEmpty()) {
                log.warn("患者 {} 的配药详情中没有有效的fyyf字段", patientName);
                return false;
            }

            log.info("患者 {} Step 3: 根据fyyf分组完成，共 {} 个药房分组", patientName, fyyfGroupMap.size());

            // Step 4: 处理每个fyyf分组
            List<InPatientDispenseDetailBindScatteredVo> allEnrichedDetails = new ArrayList<>();
            int totalAssignedCount = 0;

            for (Map.Entry<String, List<InPatientDispenseDetailBindScatteredVo>> entry : fyyfGroupMap.entrySet()) {
                String fyyf = entry.getKey();
                List<InPatientDispenseDetailBindScatteredVo> groupDetails = entry.getValue();

                log.info("患者 {} 开始处理药房 {} 的数据，数量：{}", patientName, fyyf, groupDetails.size());

                // Step 4.1: 根据fyyf获取对应的token
                String token = getTokenByFyyf(fyyf);
                if (StrUtil.isBlank(token)) {
                    log.error("患者 {} 药房 {} 获取token失败，跳过该分组", patientName, fyyf);
                    continue;
                }

                // Step 4.2: 获取拆零追溯码
                List<InPatientDispenseDetailBindScatteredVo> enrichedGroupDetails =
                        handleTracCodgStoreForInpatient(groupDetails, token);
                log.info("患者 {} 药房 {} Step 4.2: 获取拆零追溯码完成，处理 {} 条数据",
                        patientName, fyyf, enrichedGroupDetails.size());

                // Step 4.3: 追溯码赋值
                int groupAssignedCount = assignTraceCodeToEmptyRecords(unSyncedData, enrichedGroupDetails, patientName);
                log.info("患者 {} 药房 {} Step 4.3: 追溯码赋值完成，更新 {} 条记录",
                        patientName, fyyf, groupAssignedCount);
                totalAssignedCount += groupAssignedCount;

                // Step 4.4: 确认配药数据
                boolean confirmResult = confirmDispenseData(enrichedGroupDetails, token);
                if (!confirmResult) {
                    log.error("患者 {} 药房 {} Step 4.4: 确认配药数据失败", patientName, fyyf);
                    // 继续处理其他分组，不直接返回失败
                } else {
                    log.info("患者 {} 药房 {} Step 4.4: 确认配药数据完成", patientName, fyyf);
                }

                allEnrichedDetails.addAll(enrichedGroupDetails);
            }

            // Step 5: 更新数据库中的3505记录（如果有追溯码更新）
            if (totalAssignedCount > 0) {
                nhsa3505Service.updateBatchById(unSyncedData);
                log.info("患者 {} Step 5: 更新数据库记录完成，总共更新 {} 条记录", patientName, totalAssignedCount);
            }

            log.info("患者 {} 追溯码赋值和拆零确认处理完成，总共处理 {} 个药房分组", patientName, fyyfGroupMap.size());
            return true;

        } catch (Exception e) {
            log.error("患者 {} 处理追溯码赋值和拆零确认时发生异常", patientName, e);
            return false;
        }
    }

    /**
     * 根据发药药房ID(fyyf)获取对应账号的访问token
     * 从SyncAccountEnum中查找匹配的账号，然后从Redis缓存中获取或重新获取token
     *
     * @param fyyf 发药药房ID
     * @return 访问token字符串，如果获取失败返回null
     */
    private String getTokenByFyyf(String fyyf) {
        try {
            log.info("开始根据发药药房ID获取token，fyyf: {}", fyyf);

            // Step 1: 根据fyyf查找对应的账号枚举
            SyncAccountEnum accountEnum = SyncAccountEnum.findByFyyf(fyyf);
            if (accountEnum == null) {
                log.error("未找到发药药房ID对应的账号配置，fyyf: {}", fyyf);
                return null;
            }

            log.info("找到匹配的账号：{} ({})", accountEnum.getUsername(), accountEnum.getDescription());

            // Step 2: 先尝试从缓存中获取token
            AccessTokenReponse cachedToken = tokenCacheService.getTokenFromCache(accountEnum.getUsername());
            if (cachedToken != null && cachedToken.getReturnCode() == 0 && StrUtil.isNotBlank(cachedToken.getAuthorization())) {
                log.info("从缓存中获取到有效token，账号: {}", accountEnum.getUsername());
                return cachedToken.getAuthorization();
            }

            // Step 3: 缓存中没有或已过期，重新获取token
            log.info("缓存中没有有效token，重新获取，账号: {}", accountEnum.getUsername());
            AccessTokenReponse newToken = SaasHttpUtil.getAccessToken(
                    accountEnum.getUsername(),
                    accountEnum.getPassword()
            );

            if (newToken == null || newToken.getReturnCode() != 0 || StrUtil.isBlank(newToken.getAuthorization())) {
                log.error("获取token失败，账号: {}，响应: {}",
                        accountEnum.getUsername(),
                        newToken != null ? JSONUtil.toJsonStr(newToken) : "null");
                return null;
            }

            // Step 4: 缓存新获取的token
            tokenCacheService.cacheToken(accountEnum.getUsername(), newToken);
            log.info("成功获取并缓存token，账号: {}", accountEnum.getUsername());

            return newToken.getAuthorization();

        } catch (Exception e) {
            log.error("根据发药药房ID获取token异常，fyyf: {}", fyyf, e);
            return null;
        }
    }

    /**
     * 处理住院药品的追溯码库存信息
     * 参考阜阳人医的handleTracCodgStoreCydy方法逻辑
     *
     * @param extendedList 扩展的住院发药明细列表
     * @param token        令牌
     */
    public List<InPatientDispenseDetailBindScatteredVo> handleTracCodgStoreForInpatient(List<InPatientDispenseDetailBindScatteredVo> extendedList, String token) {
        try {
            // 1. 构建查询药品追溯信息的请求列表
            List<QueryTracDrugRequest> queryTracDrugRequestList = extendedList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && item.getSelRetnCnt() != null)
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedinsHilistId())
                            .dispCnt(item.getSelRetnCnt())
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                log.info("没有需要查询追溯信息的药品");
                return extendedList;
            }

            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            // 2. 处理追溯标志和转换比
            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
                    // 设置追溯标志（住院药品使用trdnFlag字段）
                    item.setTrdnFlag(queryTracDrug.getIsTrac());
                    // 设置HIS转换比例
                    item.setHisConRatio(String.valueOf(queryTracDrug.getConRatio()));

                    // 如果药品需要追溯，收集需要查询库存的药品
                    if ("1".equals(queryTracDrug.getIsTrac())) {
                        GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(item.getSelRetnCnt() != null ? item.getSelRetnCnt() : 0)
                                .drugCode(item.getFixmedinsHilistId())
                                .build();
                        tracDataList.add(build);
                    }
                }
            }

            // 3. 批量获取追溯码库存信息
            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }

            // 4. 将库存信息填充回药品对象
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if ("1".equals(item.getTrdnFlag()) && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(tracCodgStoreData.getDrugCode());
                    item.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    item.setDispCnt(tracCodgStoreData.getDispCnt());
                    item.setCurrNum(tracCodgStoreData.getCurrNum());
                    item.setTracCodgStore(tracCodgStoreData);
                }
            }

            log.info("住院药品追溯码库存信息处理完成，共处理{}条数据，其中{}条需要追溯", extendedList.size(), tracDataList.size());

        } catch (Exception e) {
            log.error("处理住院药品追溯码库存信息异常", e);
            // 不抛出异常，避免影响主流程
        }
        return extendedList;
    }

    /**
     * 对drugProdBarc字段为空的记录进行追溯码赋值
     *
     * @param unSyncedData            原始3505数据列表
     * @param enrichedDispenseDetails 包含追溯码信息的配药详情列表
     * @param patientName             患者姓名
     * @return 实际更新的记录数量
     */
    private int assignTraceCodeToEmptyRecords(List<Nhsa3505> unSyncedData,
                                              List<InPatientDispenseDetailBindScatteredVo> enrichedDispenseDetails,
                                              String patientName) {
        int assignedCount = 0;

        // 创建映射关系，以fixmedinsBchno为key
        Map<String, InPatientDispenseDetailBindScatteredVo> enrichedMap = enrichedDispenseDetails.stream()
                .filter(detail -> StringUtils.isNotEmpty(detail.getFixmedinsBchno()))
                .collect(Collectors.toMap(
                        InPatientDispenseDetailBindScatteredVo::getFixmedinsBchno,
                        Function.identity(),
                        (existing, replacement) -> replacement // 如果有重复key，使用新值
                ));

        for (Nhsa3505 nhsa3505 : unSyncedData) {
            try {
                // 只对drugProdBarc字段为空（null或空字符串）的记录进行处理
                String fixmedinsBchno = nhsa3505.getFixmedinsBchno();
                if (StringUtils.isNotEmpty(fixmedinsBchno) && enrichedMap.containsKey(fixmedinsBchno)) {
                    InPatientDispenseDetailBindScatteredVo enrichedDetail = enrichedMap.get(fixmedinsBchno);

                    // 赋值追溯码信息
                    if (enrichedDetail.getDrugTracCodgs() != null && !enrichedDetail.getDrugTracCodgs().isEmpty()) {
                        nhsa3505.setDrugTracInfo(String.join(",", enrichedDetail.getDrugTracCodgs()));
                        nhsa3505.setTrdnFlag(enrichedDetail.getTrdnFlag());
                        nhsa3505.setInvCnt(enrichedDetail.getCurrNum());
                        nhsa3505.setRemark("当日有明细自动赋码上传");
                        assignedCount++;
                        log.debug("患者 {} 为记录 {} (fixmedinsBchno: {}) 赋值追溯码：{}",
                                patientName, nhsa3505.getId(), fixmedinsBchno, enrichedDetail.getDrugCode());
                    } else {
                        nhsa3505.setInvCnt(BigDecimal.ZERO);
                        nhsa3505.setRemark("没有赋码");
                    }
                }
            } catch (Exception e) {
                log.error("患者 {} 为记录 {} 赋值追溯码时发生异常", patientName, nhsa3505.getId(), e);
            }
        }

        log.info("患者 {} 追溯码赋值完成，共处理 {} 条记录，实际赋值 {} 条", patientName, unSyncedData.size(), assignedCount);
        return assignedCount;
    }

    /**
     * 确认拆零信息
     * 调用SaaS接口确认发药数据
     *
     * @param processedDataList 已处理的数据列表
     * @param token             访问token
     * @return 确认是否成功
     */
    private boolean confirmDispenseData(List<InPatientDispenseDetailBindScatteredVo> processedDataList, String token) {
        try {
            log.info("开始确认拆零信息，数量：{}", processedDataList.size());

            // 过滤出需要确认的拆零药品
            List<InPatientDispenseDetailBindScatteredVo> confirmList = processedDataList.stream()
                    .filter(item -> "1".equals(item.getTrdnFlag()))
                    .collect(Collectors.toList());

            if (confirmList.isEmpty()) {
                log.info("无需要确认的拆零药品");
                return true;
            }

            // 构建确认发药接口请求参数
            List<ConfirmDispDrugDataRequest> requestDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo record : confirmList) {
                ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
                request.setDrugCode(record.getHisDrugCode());
                request.setDispCnt(record.getSelRetnCnt());
                request.setCfxh(record.getCfxh());
                request.setCfmxxh(record.getCfmxxh());
                requestDataList.add(request);
            }

            if (!requestDataList.isEmpty()) {
                // 构建批次请求
                ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
                confirmRequest.setDataList(requestDataList);

                // 调用confirmDispDrug接口
                SaasHttpUtil.confirmDispDrug(token, confirmRequest);
                log.info("确认拆零信息成功，确认数量：{}", requestDataList.size());
            }

            return true;

        } catch (Exception e) {
            log.error("确认拆零信息时发生异常", e);
            return false;
        }
    }

    /**
     * 处理未同步数据的赋码上传
     *
     * @param unSyncedData 未同步的3505数据列表
     * @param patient      患者信息
     * @return 处理是否成功
     */
    private boolean processUnSyncedDataUpload(List<Nhsa3505> unSyncedData, InpatientSettlementVo patient) {
        String patientName = patient.getPatientName();
        try {
            // 过滤掉drugTracInfo追溯码为空的数据
            final List<Nhsa3505> collect = unSyncedData.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getDrugTracInfo()))
                    .collect(Collectors.toList());

            log.info("开始处理患者 {} 的未同步数据上传，数量：{}", patientName, unSyncedData.size());

            // 调用现有的上传逻辑
            ApiResult<String> result = nhsa3505Service.processUpload(collect, false);
            if (result.getCode() != 200) {
                log.error("患者 {} 上传3505数据失败：{}", patientName, result.getMsg());
                return false;
            }

            log.info("患者 {} 的3505数据上传完成", patientName);
            return true;

        } catch (Exception e) {
            log.error("上传患者 {} 的3505数据时发生异常", patientName, e);
            return false;
        }
    }
}
