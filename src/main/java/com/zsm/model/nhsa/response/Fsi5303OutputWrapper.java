package com.zsm.model.nhsa.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 5303接口输出数据包装类
 * 
 * 5303接口的固定响应结构：output.data包含实际数据数组
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
@NoArgsConstructor
@Data
@Schema(description = "5303接口输出数据包装")
public class Fsi5303OutputWrapper {

    @Schema(description = "实际数据列表")
    @JsonProperty("data")
    private List<Fsi5303Response> data;

}
