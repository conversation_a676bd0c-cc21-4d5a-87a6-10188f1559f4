package com.zsm.model.nhsa.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 5303接口出参实体对象
 * 
 * 医保接口5303的响应结果
 * 
 * <AUTHOR>
 * @date 2025/1/20
 */
@NoArgsConstructor
@Data
@Schema(description = "5303接口响应信息")
public class Fsi5303Response {

    @Schema(description = "就诊ID")
    @JsonProperty("mdtrt_id")
    private String mdtrtId;

    @Schema(description = "人员编号")
    @JsonProperty("psn_no")
    private String psnNo;

    @Schema(description = "人员证件类型")
    @JsonProperty("psn_cert_type")
    private String psnCertType;

    @Schema(description = "证件号码")
    @JsonProperty("certno")
    private String certno;

    @Schema(description = "人员姓名")
    @JsonProperty("psn_name")
    private String psnName;

    @Schema(description = "性别")
    @JsonProperty("gend")
    private String gend;

    @Schema(description = "出生日期")
    @JsonProperty("brdy")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate brdy;

    @Schema(description = "年龄")
    @JsonProperty("age")
    private BigDecimal age;

    @Schema(description = "险种类型")
    @JsonProperty("insutype")
    private String insutype;

    @Schema(description = "开始日期(入院日期)")
    @JsonProperty("begndate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate begndate;

    @Schema(description = "医疗类别")
    @JsonProperty("med_type")
    private String medType;

    @Schema(description = "住院/门诊号")
    @JsonProperty("ipt_otp_no")
    private String iptOtpNo;

    @Schema(description = "异地标志")
    @JsonProperty("out_flag")
    private String outFlag;

}
