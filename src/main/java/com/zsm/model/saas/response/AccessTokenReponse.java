package com.zsm.model.saas.response;

import lombok.Data;

/**
 * SaaS系统访问令牌响应类
 * 用于封装获取访问令牌接口的响应数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class AccessTokenReponse {
    
    /**
     * 访问令牌，用于后续API调用的身份验证
     */
    private String authorization;
    
    /**
     * 返回状态码，0表示成功，其他值表示失败
     */
    private Integer returnCode;
    
    /**
     * 返回消息，描述请求结果或错误信息
     */
    private String returnMsg;
    
    /**
     * 组织机构名称
     */
    private String orgName;
    
    /**
     * 用户昵称
     */
    private String nickName;
    
    /**
     * 用户账号
     */
    private String userAccount;
    
    /**
     * 组织机构ID
     */
    private String orgID;
    
    /**
     * 发药药房类型
     */
    private String fyyfType;
    
    /**
     * 发药药房id
     */
    private String fyyfId;

    /**
     * 用户ID
     */
    private Long userId;

}