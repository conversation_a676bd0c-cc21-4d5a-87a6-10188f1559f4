package com.zsm.his.impl;

import cn.hutool.core.util.StrUtil;

import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.ViewYsfZyfycf;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.ViewYsfCyhzxx;
import com.zsm.his.InpatientTraceabilityService;
import com.zsm.mapper.ViewYsfZyfycfMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.DateRange;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.nhsa.request.fsi5204.Selinfo5204;
import com.zsm.model.nhsa.response.Fsi5204Response;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.saas.request.*;
import com.zsm.model.saas.response.AccessTokenReponse;
import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import com.zsm.model.saas.response.QueryTracDrugResponse;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.InpatientSettlementVo;
import com.zsm.service.Nhsa3505Service;
import com.zsm.service.TokenCacheService;
import com.zsm.service.ViewYsfCyhzxxService;
import com.zsm.service.ViewYsfZyfycfService;
import com.zsm.utils.*;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 阜阳肿瘤住院发药追溯码实现层
 *
 * 需求：从 VIEW_YSF_ZYFYCF 视图获取当日（或传入范围内）有发药数据的医保就诊患者，
 * 提取 mdtrt_sn 与 psn_no（若可用）调用 5204，完成追溯码赋码与上传。
 *
 * 按照 HangChuangService.processWeeklyInpatientTraceability 的业务流程实现。
 *
 * <AUTHOR>
 * @date 2025/8/19 下午9:49
 */
@Slf4j
@Service
public class InpatientTraceabilityServiceImpl implements InpatientTraceabilityService {

	@Resource
	private Nhsa3505Service nhsa3505Service;
	@Resource
	private NhsaRetryUtil nhsaRetryUtil;
	@Resource
	private SaasTokenUtil saasTokenUtil;

	@Override
	public ApiResult<String> processWeeklyInpatientTraceability(String startDate, String endDate) {


	}
}
